import { describe, it, expect, vi, beforeEach } from 'vitest';
import { wrapForMainFrameOnly } from '../../src/browser';

describe('Main Frame Script Injection', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('wrapForMainFrameOnly', () => {
    it('should wrap script with main frame check', () => {
      const originalScript = 'console.log("Hello from script");';
      const wrappedScript = wrapForMainFrameOnly(originalScript);

      expect(wrappedScript).toContain('window.parent !== window');
      expect(wrappedScript).toContain('return;');
      expect(wrappedScript).toContain(originalScript);
    });

    it('should create an IIFE wrapper', () => {
      const originalScript = 'window.myGlobal = "test";';
      const wrappedScript = wrapForMainFrameOnly(originalScript);

      expect(wrappedScript).toMatch(/^\s*\(\s*function\(\)\s*\{/);
      expect(wrappedScript).toMatch(/\}\)\(\);\s*$/);
    });

    it('should handle complex scripts', () => {
      const originalScript = `
        class MyClass {
          constructor() {
            this.value = 42;
          }
          
          method() {
            return this.value * 2;
          }
        }
        
        window.myInstance = new MyClass();
      `;
      
      const wrappedScript = wrapForMainFrameOnly(originalScript);
      
      expect(wrappedScript).toContain('window.parent !== window');
      expect(wrappedScript).toContain(originalScript);
    });
  });

  describe('Script execution behavior simulation', () => {
    it('should execute in main frame context', () => {
      // Simulate main frame (window.parent === window)
      const mockWindow = {
        parent: null as any,
        executed: false
      };
      mockWindow.parent = mockWindow; // Main frame condition

      const testScript = `
        if (window.parent !== window) {
          return;
        }
        window.executed = true;
      `;

      // Simulate script execution
      const func = new Function('window', testScript);
      func(mockWindow);

      expect(mockWindow.executed).toBe(true);
    });

    it('should not execute in iframe context', () => {
      // Simulate iframe (window.parent !== window)
      const mockParentWindow = { executed: false };
      const mockIframeWindow = {
        parent: mockParentWindow,
        executed: false
      };

      const testScript = `
        if (window.parent !== window) {
          return;
        }
        window.executed = true;
      `;

      // Simulate script execution
      const func = new Function('window', testScript);
      func(mockIframeWindow);

      expect(mockIframeWindow.executed).toBe(false);
    });
  });

  describe('Integration with CDP addScriptToEvaluateOnNewDocument', () => {
    it('should work with CDP script injection pattern', () => {
      const mockCDP = {
        Page: {
          addScriptToEvaluateOnNewDocument: vi.fn().mockResolvedValue({ identifier: 'script-123' })
        }
      };

      const originalScript = 'window.myFeature = { enabled: true };';
      const wrappedScript = wrapForMainFrameOnly(originalScript);

      // Simulate the injection call
      mockCDP.Page.addScriptToEvaluateOnNewDocument({
        source: wrappedScript,
        worldName: 'kaku-world'
      }, 'session-id');

      expect(mockCDP.Page.addScriptToEvaluateOnNewDocument).toHaveBeenCalledWith(
        expect.objectContaining({
          source: expect.stringContaining('window.parent !== window'),
          worldName: 'kaku-world'
        }),
        'session-id'
      );
    });
  });
});
