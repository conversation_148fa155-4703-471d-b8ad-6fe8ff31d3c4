import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';

/**
 * Integration tests for PII redaction in the browser automation workflow
 * These tests verify the end-to-end integration of PII redaction with screenshot capture
 */

describe('PII Redaction Workflow Integration', () => {
  let mockCDP: any;
  let mockCommunicator: any;

  beforeEach(() => {
    // Mock CDP client
    mockCDP = {
      Page: {
        captureScreenshot: vi.fn().mockResolvedValue({ data: 'base64-screenshot-data' }),
        addScriptToEvaluateOnNewDocument: vi.fn().mockResolvedValue({ identifier: 'script-id' }),
        getLayoutMetrics: vi.fn().mockResolvedValue({
          cssLayoutViewport: { clientWidth: 1024, clientHeight: 768 },
          contentSize: { width: 1024, height: 768 },
        }),
      },
      Runtime: {
        evaluate: vi.fn(),
      },
    };

    // Mock cross-tab communicator
    mockCommunicator = {
      sendMessage: vi.fn(),
      onMessage: vi.fn(),
    };

    // Mock global objects
    (global as any).performance = {
      now: vi.fn(() => Date.now()),
    };

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Script Injection', () => {
    it('should inject PII redactor as Phase 1 essential script', async () => {
      const mockFetch = vi
        .fn()
        .mockResolvedValueOnce({ text: () => Promise.resolve('// pii-redactor script') })
        .mockResolvedValueOnce({
          text: () => Promise.resolve('// browser-controller-proxy script'),
        });

      (global as any).fetch = mockFetch;

      // Simulate script injection
      const piiRedactorUrl = 'https://api.example.com/pii-redactor.min.js';
      const browserControllerProxyUrl = 'https://api.example.com/browser-controller-proxy.min.js';

      await Promise.all([
        fetch(piiRedactorUrl).then((r) => r.text()),
        fetch(browserControllerProxyUrl).then((r) => r.text()),
      ]);

      expect(mockFetch).toHaveBeenCalledTimes(2);
      expect(mockFetch).toHaveBeenCalledWith(piiRedactorUrl);
      expect(mockFetch).toHaveBeenCalledWith(browserControllerProxyUrl);
    });

    it('should inject scripts with correct world name', () => {
      const targetSessionId = 'target-session-123';
      const worldName = 'kaku-target-world';

      // Simulate addScriptToEvaluateOnNewDocument calls
      mockCDP.Page.addScriptToEvaluateOnNewDocument(
        {
          source: '// pii-redactor script',
          worldName,
        },
        targetSessionId,
      );

      mockCDP.Page.addScriptToEvaluateOnNewDocument(
        {
          source: '// browser-controller-proxy script',
          worldName,
        },
        targetSessionId,
      );

      expect(mockCDP.Page.addScriptToEvaluateOnNewDocument).toHaveBeenCalledTimes(2);
      expect(mockCDP.Page.addScriptToEvaluateOnNewDocument).toHaveBeenCalledWith(
        expect.objectContaining({ worldName }),
        targetSessionId,
      );
    });
  });

  describe('Cross-Tab Communication', () => {
    it('should handle PII redaction messages', async () => {
      const mockRedactionResult = {
        success: true,
        data: {
          redactedCount: 3,
          processingTime: 150,
          mappings: [
            {
              xpath: '/html/body/p/text()[1]',
              originalText: '<EMAIL>',
              redactedText: '***REDACTED***',
              elementType: 'text',
            },
            {
              xpath: '/html/body/input[1]',
              originalText: '<EMAIL>',
              redactedText: '***REDACTED***',
              elementType: 'input',
            },
            {
              xpath: '/html/body/div/text()[1]',
              originalText: '555-123-4567',
              redactedText: '***REDACTED***',
              elementType: 'text',
            },
          ],
        },
      };

      mockCommunicator.sendMessage.mockResolvedValue(mockRedactionResult);

      const result = await mockCommunicator.sendMessage('redactPII');

      expect(mockCommunicator.sendMessage).toHaveBeenCalledWith('redactPII');
      expect(result.success).toBe(true);
      expect(result.data.redactedCount).toBe(3);
    });

    it('should handle PII restoration messages', async () => {
      const mockRestorationResult = {
        success: true,
        data: {
          restoredCount: 3,
          failedCount: 0,
          processingTime: 75,
          errors: [],
        },
      };

      mockCommunicator.sendMessage.mockResolvedValue(mockRestorationResult);

      const result = await mockCommunicator.sendMessage('restorePII');

      expect(mockCommunicator.sendMessage).toHaveBeenCalledWith('restorePII');
      expect(result.success).toBe(true);
      expect(result.data.restoredCount).toBe(3);
      expect(result.data.failedCount).toBe(0);
    });

    it('should handle PII status requests', async () => {
      const mockStatusResult = {
        success: true,
        data: {
          isRedacted: true,
          mappingCount: 3,
          available: true,
        },
      };

      mockCommunicator.sendMessage.mockResolvedValue(mockStatusResult);

      const result = await mockCommunicator.sendMessage('getPIIRedactionStatus');

      expect(mockCommunicator.sendMessage).toHaveBeenCalledWith('getPIIRedactionStatus');
      expect(result.success).toBe(true);
      expect(result.data.isRedacted).toBe(true);
      expect(result.data.available).toBe(true);
    });
  });

  describe('Screenshot Capture with Redaction', () => {
    it('should capture screenshot with PII redaction workflow', async () => {
      const controlTabSessionId = 'control-session-123';

      // Mock the Runtime.evaluate call for captureScreenshotWithRedaction
      const mockEvaluateResult = {
        result: {
          value: {
            success: true,
            data: 'base64-redacted-screenshot-data',
            timestamp: Date.now(),
            redactionApplied: true,
            redactionStats: {
              redactedCount: 2,
              processingTime: 200,
            },
            processingTime: 350,
          },
        },
      };

      mockCDP.Runtime.evaluate.mockResolvedValue(mockEvaluateResult);

      // Simulate the workflow's captureScreenshot method with redaction
      const result = await mockCDP.Runtime.evaluate(
        {
          expression: expect.stringContaining('captureScreenshotWithRedaction'),
          awaitPromise: true,
          returnByValue: true,
        },
        controlTabSessionId,
      );

      expect(mockCDP.Runtime.evaluate).toHaveBeenCalledWith(
        expect.objectContaining({
          expression: expect.stringContaining('captureScreenshotWithRedaction'),
          awaitPromise: true,
          returnByValue: true,
        }),
        controlTabSessionId,
      );

      expect(result.result.value.success).toBe(true);
      expect(result.result.value.redactionApplied).toBe(true);
      expect(result.result.value.data).toBe('base64-redacted-screenshot-data');
    });

    it('should fallback to standard screenshot on redaction failure', async () => {
      const targetSessionId = 'target-session-123';

      // Mock Runtime.evaluate to simulate redaction failure
      mockCDP.Runtime.evaluate.mockResolvedValue({
        result: {
          value: {
            success: false,
            error: 'PII redactor not available',
            fallback: true,
          },
        },
      });

      // Mock standard screenshot capture
      mockCDP.Page.captureScreenshot.mockResolvedValue({
        data: 'base64-standard-screenshot-data',
      });

      // Simulate fallback to standard screenshot
      const screenshotResult = await mockCDP.Page.captureScreenshot(
        {
          format: 'webp',
          captureBeyondViewport: false,
        },
        targetSessionId,
      );

      expect(mockCDP.Page.captureScreenshot).toHaveBeenCalledWith(
        {
          format: 'webp',
          captureBeyondViewport: false,
        },
        targetSessionId,
      );

      expect(screenshotResult.data).toBe('base64-standard-screenshot-data');
    });
  });

  describe('Error Handling and Fallbacks', () => {
    it('should handle communication timeouts gracefully', async () => {
      const timeoutError = new Error('Communication timeout for message type: redactPII');
      mockCommunicator.sendMessage.mockRejectedValue(timeoutError);

      try {
        await mockCommunicator.sendMessage('redactPII');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as any).message).toContain('timeout');
      }
    });

    it('should handle script injection failures', async () => {
      const mockFetch = vi.fn().mockRejectedValue(new Error('Script fetch failed'));
      (global as any).fetch = mockFetch;

      try {
        await fetch('https://api.example.com/pii-redactor.min.js');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as any).message).toBe('Script fetch failed');
      }
    });

    it('should handle CDP evaluation errors', async () => {
      const cdpError = new Error('CDP evaluation failed');
      mockCDP.Runtime.evaluate.mockRejectedValue(cdpError);

      try {
        await mockCDP.Runtime.evaluate(
          {
            expression: 'window.persistentCDPController.captureScreenshotWithRedaction(true)',
            awaitPromise: true,
            returnByValue: true,
          },
          'control-session-123',
        );
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as any).message).toBe('CDP evaluation failed');
      }
    });
  });

  describe('Performance Requirements', () => {
    it('should complete redaction within 500ms target', () => {
      const mockRedactionResult = {
        success: true,
        data: {
          redactedCount: 5,
          processingTime: 450, // Within 500ms target
        },
      };

      expect(mockRedactionResult.data.processingTime).toBeLessThan(500);
    });

    it('should log performance warnings when exceeding target', () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const slowRedactionResult = {
        success: true,
        data: {
          redactedCount: 10,
          processingTime: 750, // Exceeds 500ms target
        },
      };

      if (slowRedactionResult.data.processingTime > 500) {
        console.error(
          `PII redaction exceeded performance target: ${slowRedactionResult.data.processingTime}ms > 500ms`,
        );
      }

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('exceeded performance target'),
      );
    });
  });
});
