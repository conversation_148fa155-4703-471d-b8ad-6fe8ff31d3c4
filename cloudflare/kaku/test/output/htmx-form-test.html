<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTMX Form Generator Test - Sign in</title>
    <script src="https://unpkg.com/htmx.org@2.0.4"></script>
    <script src="https://unpkg.com/htmx.org/dist/ext/ws.js"></script>
    <link rel="stylesheet" href="../../public/css/styles.css" />
    <link rel="stylesheet" href="../../public/css/video-layout.css" />
    <link rel="stylesheet" href="../../public/css/bottom-sheet.css" />
</head>
<body class="light bg-neutral-60 dark:bg-primary-50 h-screen flex items-center justify-center">
    <div class="relative md:w-[435px] md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
        <div id="connection-flow-wrapper" class="h-screen flex flex-col justify-center items-center relative md:w-[435px] md:h-auto md:rounded-xl shadow-md shadow-neutral-50 bg-surface">
            <div id="connection-flow" class="w-screen flex flex-col items-center justify-center bg-surface text-surface-on-surface md:h-[auto] md:min-h-[600px] md:w-[435px] md:rounded-xl p-8">
                <div class="text-start w-full">
                    <h1 class="text-xl font-semibold mb-4">Sign in</h1>
                    <p class="text-base mb-2">Use your Google Account</p>
                </div>
                <form class="form-container" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML">
<div class="prompt-container">
      <div class="contextual-info">
        <div class="contextual-info-text">Please enter your email or phone number.</div>
      </div>
    </div>

    <div class="input-container">
      <div class="floating-input-wrapper">
        <input
          class="floating-input"
          name="identifier"
          type="text"
          placeholder=""
          id="email-address"
        >
        <label class="floating-label" for="email-address">Email or phone</label>
      </div>
    </div>
  

    <div class="button-container">
      <button
        type="submit"
        class="button-primary"
        hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-vals='{"clickId": "next", "interaction": "submit"}'
      >
        Next
      </button>
    </div>
  
</form>

               <div style="margin-top: 2rem; padding: 1rem; background-color: #f0f0f0; border-radius: 0.5rem; width: 100%;">
  <h3 style="font-size: 0.875rem; font-weight: 600; color: #1c1b1f; margin-bottom: 0.5rem;">
    Form Analysis Data (Simplified Schema):
  </h3>
  <details style="font-size: 0.75rem;">
    <summary style="cursor: pointer; color: #6750a4; margin-bottom: 0.5rem; transition: color 0.2s;"
             onmouseover="this.style.color='#5f4da5'"
             onmouseout="this.style.color='#6750a4'">
      View Raw JSON
    </summary>
    <pre style="margin-top: 0.5rem; padding: 0.5rem; background-color: #ffffff; border: 1px solid #cac4d0; border-radius: 0.25rem; font-size: 0.75rem; overflow: auto; max-height: 10rem; color: #1c1b1f;">
      {
  "metadata": {
    "title": "Sign in",
    "description": "Use your Google Account",
    "prompt": "Please enter your email or phone number.",
    "promptCode": null,
    "errors": [],
    "pageType": "not-authenticated"
  },
  "controls": {
    "fields": [
      {
        "id": "email-address",
        "order": 1,
        "actor": "human",
        "label": "Email or phone",
        "type": "text",
        "actiontype": "fill",
        "name": "identifier",
        "options": null,
        "readOnly": false
      }
    ],
    "buttons": [
      {
        "id": "next",
        "order": 3,
        "actor": "human",
        "label": "Next",
        "variant": "primary",
        "type": "submit",
        "actiontype": "click",
        "synthetic": false
      }
    ]
  }
}
    </pre>
  </details>
</div>

            </div>
        </div>
    </div>
</body>
</html>