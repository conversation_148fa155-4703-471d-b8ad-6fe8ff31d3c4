import { AnthropicLLMRepository } from '../../src/llm/AnthropicLLMRepository';
import { LLMService } from '../../src/llm/LLMService';
import { OpenAILLMRepository } from '../../src/llm/OpenAILLMRepository';
import {
  htmxPromptInstructions,
  monolithicFormPromptInstructions,
  pageDetailsPromptInstructions,
} from '../../src/workflow/utils/constants';
import { extractFormJSON } from '../../src/workflow/utils/helpers';
import { imageToBase64 } from '../common/ImageHelpers';

const openAIKey = ''; //TODO(Insert the openAI key here to run this script)
const anthropicApiKey = ''; //TODO(Insert the anthropic key here to run this script)
const noOfRacingLLMCalls = 2;
const openAIBaseURL =
  'https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/openai';
const anthropicBaseURL =
  'https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/anthropic';

export async function testOpenAIFormGeneration() {
  //Check to confirm that the user has added the openAI key
  if (!openAIKey) {
    console.log('Insert the openAI api key to continue..');
    return;
  }
  if (!anthropicApiKey) {
    console.log('Insert the Anthropic api key to continue..');
    return;
  }
  const facebookLoginScreenshot = await imageToBase64(
    `${__dirname.replace('/scripts', '/files/screenshots/facebook_login.webp')}`,
  );
  const openAIRepository = new OpenAILLMRepository(openAIKey, openAIBaseURL);
  const racingOpenAIService = new LLMService({
    primaryRepo: openAIRepository,
    secondaryRepo: openAIRepository,
  });
  const anthropicLLMRepository = new AnthropicLLMRepository(anthropicApiKey, anthropicBaseURL);
  const racingAnthropicService = new LLMService({
    primaryRepo: anthropicLLMRepository,
    secondaryRepo: anthropicLLMRepository,
  });

  console.log('-OpenAI-');
  console.log('Loading...');

  const openAIResponse = await racingOpenAIService.raceLLMGetCalls(
    {
      platform: 'facebook',
      prompt: monolithicFormPromptInstructions,
      screenshot: facebookLoginScreenshot,
      skipCache: true,
      viewportWidth: 800,
      viewportHeight: 600,
    },
    noOfRacingLLMCalls,
    true,
  );

  console.log(`\n\nOpenAI Duration: ${openAIResponse.callDuration}ms`);
  console.log(`\nOpenAI Response ${JSON.stringify(openAIResponse.output_text)}\n`);

  console.log('\n-ANTHROPIC-CLAUDE- ');
  console.log('Loading...');

  const claudeResponse = await racingAnthropicService.raceLLMGetCalls(
    {
      platform: 'facebook',
      prompt: monolithicFormPromptInstructions,
      screenshot: facebookLoginScreenshot,
      skipCache: true,
      viewportWidth: 800,
      viewportHeight: 600,
    },
    noOfRacingLLMCalls,
    true,
  );
  console.log(`\n\nAnthropic Duration: ${claudeResponse.callDuration}ms`);
  console.log(`\nAnthropic Response: ${JSON.stringify(claudeResponse.output_text)}\n`);
}

export async function testOpenAIIncorrectGoogleAuthStatus() {
  //Check to confirm that the user has added the openAI key
  if (!openAIKey) {
    console.log('Insert the openAI api key to continue..');
    return;
  }

  const googleErrorScreenshot = await imageToBase64(
    `${__dirname.replace('/scripts', '/files/screenshots/google_error.png')}`,
  );

  const openAIRepository = new OpenAILLMRepository(openAIKey, openAIBaseURL);

  const numberOfTests = 15;

  const pageTypeResults: string[] = [];

  for (let i = 1; i <= numberOfTests; i++) {
    try {
      const openAIResponse = await openAIRepository.getLLMResponse({
        platform: 'google',
        prompt: monolithicFormPromptInstructions,
        screenshot: googleErrorScreenshot,
        skipCache: true,
        viewportWidth: 800,
        viewportHeight: 600,
      });

      const result = extractFormJSON(openAIResponse.output_text);
      console.log(`\nTest ${i} - OpenAI Response:`, result.pageType);
      pageTypeResults.push(result.pageType);
    } catch (error) {
      console.error(`❌ Test ${i} failed with an error:`, error);
    }
  }

  console.log('\n\nAll Page Type Results:', pageTypeResults);

  const pageTypeCount: Record<string, number> = {};
  pageTypeResults.forEach((type) => {
    if (pageTypeCount[type]) {
      pageTypeCount[type]++;
    } else {
      pageTypeCount[type] = 1;
    }
  });

  console.log('\nPage Type Count:', pageTypeCount);
  console.log('\n✅ All test completed.');
}

export async function measureSingleCallVsHTMXAndActionsAsTwoParallelCalls() {
  if (!openAIKey) {
    console.log('Insert the openAI api key in measureLLMCallDuration.ts file to continue..');
    return;
  }

  const facebookLoginScreenshot = await imageToBase64(
    `${__dirname.replace('/scripts', '/files/screenshots/facebook_login.webp')}`,
  );

  const llmRepository = new OpenAILLMRepository(openAIKey, openAIBaseURL);

  const llmService = new LLMService({
    primaryRepo: llmRepository,
    secondaryRepo: llmRepository,
  });

  console.log(`Single API call loading...`);

  const singleAPICallResponse = await llmService.raceLLMGetCalls(
    {
      platform: 'facebook',
      prompt: monolithicFormPromptInstructions,
      screenshot: facebookLoginScreenshot,
      skipCache: true,
      viewportWidth: 800,
      viewportHeight: 600,
    },
    noOfRacingLLMCalls,
    true,
  );

  console.log(`Single call response: ${singleAPICallResponse.output_text}`);
  console.log(`\n\nSingle Call Duration: ${singleAPICallResponse.callDuration}ms\n\n`);

  console.log(`Parallel calls loading...`);

  const htmxCallPromise = llmService.raceLLMGetCalls(
    {
      platform: 'facebook',
      prompt: htmxPromptInstructions,
      screenshot: facebookLoginScreenshot,
      skipCache: true,
      viewportWidth: 800,
      viewportHeight: 600,
    },
    noOfRacingLLMCalls,
    true,
  );

  const formFieldsCallPromise = llmService.raceLLMGetCalls(
    {
      platform: 'facebook',
      prompt: pageDetailsPromptInstructions,
      screenshot: facebookLoginScreenshot,
      skipCache: true,
      viewportWidth: 800,
      viewportHeight: 600,
    },
    noOfRacingLLMCalls,
    true,
  );

  const htmxValue = await htmxCallPromise;
  const formFieldsValue = await formFieldsCallPromise;

  const highestParallelDuration: number =
    htmxValue.callDuration > formFieldsValue.callDuration
      ? htmxValue.callDuration
      : formFieldsValue.callDuration;

  console.log(`HTMX Response: ${htmxValue.output_text}\n\n`);
  console.log(`FormFields Response: ${formFieldsValue.output_text}`);

  console.log(`\nSingle Call Duration: ${singleAPICallResponse.callDuration}ms`);
  console.log(`\nHTMX Call Duration: ${htmxValue.callDuration}ms`);
  console.log(`FormFields Call Duration: ${formFieldsValue.callDuration}ms\n`);

  console.log(
    `Single call took ${singleAPICallResponse.callDuration}ms and Parallel calls took ${highestParallelDuration}ms`,
  );
  console.log(`Difference is ${singleAPICallResponse.callDuration - highestParallelDuration}ms`);
}

const action = process.argv[2];

switch (action) {
  case 'testOpenAIFormGeneration':
    testOpenAIFormGeneration();
    break;
  case 'testOpenAIIncorrectGoogleAuthStatus':
    testOpenAIIncorrectGoogleAuthStatus();
    break;
  case 'measureParallelCallsPerformance':
    measureSingleCallVsHTMXAndActionsAsTwoParallelCalls();
    break;
  default:
    console.error(`Unknown or missing action: "${action}"`);
    process.exit(1);
}
