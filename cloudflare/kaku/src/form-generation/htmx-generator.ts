/**
 * HTMX Form Generator
 * Converts FormVisionResult schema to HTMX markup
 */

export type {
  FormVisionResult,
  FormMetadata,
  FormControls,
  FormField,
  FormButton,
} from './types/form-interfaces';

import {
  generateFormOpenTag,
  generateFormCloseTag,
  generatePromptDisplay,
  getErrorMessages,
  generateErrorMessages,
  generateFloatingLabelField,
  generateCheckboxField,
  generateRadioField,
  generateTextareaField,
  generateStandard<PERSON>ield,
  generateFormButton,
} from './components';
import type { FormVisionResult, FormField } from './types/form-interfaces';

/**
 * Generates HTMX form markup from FormVisionResult
 */
export class HTMXFormGenerator {
  generateForm(formData: FormVisionResult): string {
    const { metadata, controls } = formData;

    // Filter controls by actor - only include human-interactive elements
    const humanFields = controls.fields.filter(
      (field) => !field.readOnly && field.actor === 'human',
    );
    const humanButtons = controls.buttons.filter((button) => button.actor === 'human');

    const formElements: string[] = [];

    formElements.push(generateFormOpenTag());

    const errorMessages = getErrorMessages(metadata);

    // Add prompt/verification codes prominently if present
    if ((metadata.prompt || metadata.promptCode) && errorMessages.length === 0) {
      formElements.push(generatePromptDisplay(metadata.prompt, metadata.promptCode));
    }

    if (errorMessages.length > 0) {
      formElements.push(generateErrorMessages(errorMessages));
    }

    // Generate form fields (human-interactive only)
    humanFields.forEach((field) => {
      formElements.push(this.generateFormField(field));
    });

    humanButtons.forEach((button) => {
      formElements.push(generateFormButton(button));
    });

    formElements.push(generateFormCloseTag());

    return formElements.join('\n');
  }

  /**
   * Generate form field based on field type
   */
  private generateFormField(field: FormField): string {
    switch (field.type) {
      case 'text':
      case 'password':
      case 'email':
      case 'number':
        return generateFloatingLabelField(field);
      case 'checkbox':
        return generateCheckboxField(field);
      case 'radio':
        return generateRadioField(field);
      case 'textarea':
        return generateTextareaField(field);
      default:
        return generateStandardField(field);
    }
  }
}

// Export singleton instance
export const htmxFormGenerator = new HTMXFormGenerator();
