/**
 * Textarea field component
 */

import type { FormField } from '../../types';

/**
 * Generate textarea field markup
 */
export function generateTextareaField(field: FormField): string {
  return `
    <div class="input-container">
      <label class="form-label" for="${field.id}">${field.label}</label>
      <textarea
        class="input-field"
        id="${field.id}"
        name="${field.name}"
      ></textarea>
    </div>
  `;
}
