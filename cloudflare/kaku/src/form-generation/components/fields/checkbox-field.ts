/**
 * Checkbox field component
 */

import type { FormField } from '../../types';

/**
 * Generate checkbox field markup
 */
export function generateCheckboxField(field: FormField): string {
  return `
    <div class="input-container">
      <div class="checkbox-container">
        <input
          class="checkbox-field"
          type="checkbox"
          id="${field.id}"
          name="${field.name}"
        >
        <label class="checkbox-label" for="${field.id}">${field.label}</label>
      </div>
    </div>
  `;
}
