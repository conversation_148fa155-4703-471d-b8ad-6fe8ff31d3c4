/**
 * Prompt display component
 */

/**
 * Generate prompt display markup for verification codes and contextual information
 */
export function generatePromptDisplay(prompt: string | null, promptCode: string | null): string {
  if (!prompt && !promptCode) return '';

  const elements: string[] = [];

  if (promptCode) {
    elements.push(`
      <div class="verification-code-display">
        <div class="verification-code-label">Verification Code</div>
        <div class="verification-code-value">${promptCode}</div>
      </div>
    `);
  }

  if (prompt) {
    elements.push(`
      <div class="contextual-info">
        <div class="contextual-info-text">${prompt}</div>
      </div>
    `);
  }

  return `<div class="prompt-container">${elements.join('')}</div>`;
}
