import { RemoteBrowserService } from '../adapters/BrowserDataAdapter';
import { LocalBrowserService } from './LocalBrowserService';
import { HyperbrowserService } from './HyperbrowserService';

/**
 * Configuration for browser service creation
 */
export interface BrowserServiceConfig {
  environment: string;
  hyperbrowserApiKey?: string;
  hyperbrowserTimeout?: number;
  localDebugPort?: number;
  localDebugHost?: string;
}

/**
 * Factory for creating appropriate browser service based on environment
 * Provides a clean abstraction over local vs remote browser connections
 */
export class BrowserServiceFactory {
  /**
   * Create a browser service based on the environment configuration
   */
  static createService(config: BrowserServiceConfig): RemoteBrowserService {
    if (config.environment === 'local') {
      console.log('→ Creating LocalBrowserService for local development');
      return new LocalBrowserService(
        config.localDebugPort || 9222,
        config.localDebugHost || 'localhost',
      );
    } else {
      console.log('→ Creating HyperbrowserService for remote browser sessions');

      if (!config.hyperbrowserApiKey) {
        throw new Error('Hyperbrowser API key is required for remote browser sessions');
      }

      return new HyperbrowserService(
        config.hyperbrowserApiKey,
        config.hyperbrowserTimeout || 60000,
      );
    }
  }

  /**
   * Convenience method to create service from environment variables
   * This matches the existing pattern used in the codebase
   */
  static createFromEnvironment(env: {
    ENVIRONMENT: string;
    HYPERBROWSER_API_KEY?: string;
  }): RemoteBrowserService {
    return BrowserServiceFactory.createService({
      environment: 'dev',
      hyperbrowserApiKey: 'hb_28aac10409666bbccf859a9b8804',
    });
  }
}
