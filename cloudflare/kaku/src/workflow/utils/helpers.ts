import * as UPNG from 'upng-js';
import pixelmatch from 'pixelmatch';
import { PageStateResult } from '../../agent/types/extract-result';

export function extractFormJSON(content: string): PageStateResult {
  try {
    const cleanContent = content
      .replace(/```json\s*/g, '') // Remove ```json
      .replace(/```\s*$/g, '') // Remove closing ```
      .trim(); // Remove extra whitespace

    return JSON.parse(cleanContent);
  } catch (error) {
    console.error('Error parsing form JSON:', error);
    console.error('Raw content:', content);
    throw Error(`Bad json ${content}`);
  }
}

export function generatePageStateResultFromMultipleLLMCalls(
  htmxContent: string,
  formFieldsContent: string,
): PageStateResult {
  try {
    const cleanFormFieldContent = formFieldsContent
      .replace(/```json\s*/g, '') // Remove ```json
      .replace(/```\s*$/g, '') // Remove closing ```
      .trim(); // Remove extra whitespace

    const cleanHtmxContent = htmxContent
      .replace(/```\s*$/g, '') // Remove closing ```
      .trim(); // Remove extra whitespace

    const jsonResponse = JSON.parse(cleanFormFieldContent);
    jsonResponse.htmxForm = cleanHtmxContent;
    return jsonResponse;
  } catch (error) {
    console.error('Error parsing form JSON:', error);
    console.error('Form field Content:', formFieldsContent);
    console.error('HTMX content:', htmxContent);
    throw Error(`Bad json: FormField ${formFieldsContent}, HTMX ${htmxContent}`);
  }
}

export function capitalize(str: string): string {
  if (!str) return '';
  return str[0].toUpperCase() + str.slice(1);
}

/**
 * Utility function to race a promise with a timeout
 * @param promise The promise to race
 * @param timeoutMs Timeout in milliseconds
 * @returns Promise that resolves with the original promise or timeout
 */
export function withTimeout<T>(promise: Promise<T>, timeoutMs: number): Promise<unknown> {
  return Promise.race([
    promise,
    new Promise((resolve) => setTimeout(() => resolve(true), timeoutMs)),
  ]);
}

/**
 * Simple sleep utility function
 * @param timeInMillis Time to sleep in milliseconds
 * @returns Promise that resolves after the specified time
 */
export function sleep(timeInMillis: number): Promise<void> {
  return new Promise((r) => setTimeout(r, timeInMillis));
}

/**
 * Converts base64 PNG to image data using upng-js (Workers-compatible)
 * @param base64 Base64 encoded PNG image
 * @returns Image data with RGBA pixels, width, and height
 */
export async function base64ToImageData(
  base64: string,
): Promise<{ data: Uint8ClampedArray; width: number; height: number } | null> {
  try {
    const cleanBase64 = base64.replace(/^data:image\/[^;]+;base64,/, '');
    const binaryString = atob(cleanBase64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    const img = UPNG.decode(bytes.buffer);
    const rgba = UPNG.toRGBA8(img)[0];

    return {
      data: new Uint8ClampedArray(rgba),
      width: img.width,
      height: img.height,
    };
  } catch (error) {
    console.warn('Failed to convert base64 to ImageData:', error);
    return null;
  }
}

/**
 * Compares two base64 PNG images using pixelmatch in Cloudflare Workers
 * Uses upng-js for Workers-compatible PNG decoding
 * @param base64Img1 First base64 PNG image
 * @param base64Img2 Second base64 PNG image
 * @returns Comparison result with percentage difference and timing
 */
export async function compareScreenshots(
  base64Img1: string,
  base64Img2: string,
): Promise<{ percentageDiff: number; comparisonTime: number } | null> {
  try {
    const startTime = performance.now();

    if (base64Img1 === base64Img2) {
      return {
        percentageDiff: 0,
        comparisonTime: performance.now() - startTime,
      };
    }

    const [img1, img2] = await Promise.all([
      base64ToImageData(base64Img1),
      base64ToImageData(base64Img2),
    ]);

    if (!img1 || !img2) {
      console.warn('Failed to decode one or both images');
      return null;
    }

    if (img1.width !== img2.width || img1.height !== img2.height) {
      console.warn(
        `Image dimensions do not match: img1(${img1.width}x${img1.height}) vs img2(${img2.width}x${img2.height})`,
      );
      return null;
    }

    const diff = new Uint8ClampedArray(img1.data.length);

    const mismatchedPixels = pixelmatch(img1.data, img2.data, diff, img1.width, img1.height, {
      threshold: 0.1,
      includeAA: false,
    });

    const totalPixels = img1.width * img1.height;
    const percentageDiff = totalPixels > 0 ? (mismatchedPixels / totalPixels) * 100 : 0;

    const comparisonTime = performance.now() - startTime;

    return {
      percentageDiff,
      comparisonTime,
    };
  } catch (error) {
    console.warn('Failed to compare screenshots with pixelmatch:', error);
    return null;
  }
}
