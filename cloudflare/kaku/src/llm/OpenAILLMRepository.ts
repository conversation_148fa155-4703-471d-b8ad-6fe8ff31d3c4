import OpenAI from 'openai';
import { LLMRepository } from './LLMRepository';
import { LLMResponse } from './types/llm-response';
import { generateCacheKeyFromScreenshot } from '../common/utils';
import { LLMRequest } from './types/llm-request';
export class OpenAILLMRepository implements LLMRepository {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey: string, baseURL: string) {
    this.apiKey = apiKey;
    this.baseURL = baseURL;
  }

  async getLLMResponse(llmRequest: LLMRequest): Promise<LLMResponse> {
    const start = Date.now();
    const cacheKey = generateCacheKeyFromScreenshot(llmRequest, llmRequest.version);

    const openAIClient = new OpenAI({
      apiKey: this.apiKey,
      baseURL: this.baseURL,
      defaultHeaders: {
        'cf-aig-cache-key': cacheKey,
        'cf-aig-skip-cache': llmRequest.skipCache.toString(),
      },
    });

    const response = await openAIClient.responses.create({
      model: 'computer-use-preview',
      truncation: 'auto',
      tools: [
        {
          type: 'computer-preview',
          display_width: llmRequest.viewportWidth,
          display_height: llmRequest.viewportHeight,
          environment: 'browser',
        },
      ],
      instructions: llmRequest.prompt,
      input: [
        {
          role: 'user',
          content: [
            {
              type: 'input_image',
              detail: 'low',
              image_url: `data:image/jpeg;base64,${llmRequest.screenshot}`,
            },
          ],
        },
      ],
    });
    const end = Date.now();
    const duration = end - start;

    return {
      callDuration: duration,
      output_text: response.output_text,
    };
  }
}
