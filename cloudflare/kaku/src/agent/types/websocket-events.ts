/**
 * WebSocket event types for consistent event handling
 */

/**
 * Base interface for all WebSocket events
 */
export interface BaseWebSocketEvent {
  type: string;
  [key: string]: any;
}

/**
 * Terms and conditions events
 */
export interface AgreeAndContinueEvent extends BaseWebSocketEvent {
  type: 'agree_and_continue';
}

export interface DeclineTermsEvent extends BaseWebSocketEvent {
  type: 'decline_terms';
}

/**
 * Form submission events
 */
export interface FormSubmissionEvent extends BaseWebSocketEvent {
  type: 'form_submission';
  clickId: string;
  interaction: 'submit' | 'click';
  [fieldName: string]: string | any; // Form field values
}

/**
 * UI interaction events
 */
export interface CropBoxUpdateEvent extends BaseWebSocketEvent {
  type: 'cropbox-update';
  // Add specific cropbox update properties here
}

export interface RetryEvent extends BaseWebSocketEvent {
  type: 'retry';
}

/**
 * Union type of all possible WebSocket events
 */
export type WebSocketEvent =
  | AgreeAndContinueEvent
  | DeclineTermsEvent
  | FormSubmissionEvent
  | CropBoxUpdateEvent
  | RetryEvent;

/**
 * Event handler type definitions
 */
export type EventHandler<T extends BaseWebSocketEvent> = (
  event: T,
  connection?: any,
) => Promise<void> | void;

export interface EventHandlers {
  agree_and_continue: EventHandler<AgreeAndContinueEvent>;
  decline_terms: EventHandler<DeclineTermsEvent>;
  form_submission: EventHandler<FormSubmissionEvent>;
  'cropbox-update': EventHandler<CropBoxUpdateEvent>;
  retry: EventHandler<RetryEvent>;
}
