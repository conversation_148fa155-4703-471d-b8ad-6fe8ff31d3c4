import Protocol from 'devtools-protocol';
import { CDP } from '../browser/simple-cdp';
import { CrossTabCommunicator } from './utils/cross-tab-communicator';
import { K_CUSTOM_VIEWPORT } from '../workflow/utils/constants';
import { convertToGrayscale } from '../common/utils/image-processing';

declare global {
  interface Window {
    persistentCDPController: any;
  }
}

const CHROME_HEADER = 88;
const CHROME_SCREEN_SHARING_BANNER = 55;

class PersistentCDPError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly operation?: string,
  ) {
    super(`[kazeel][persistent-cdp-controller] ${message}`);
    this.name = 'PersistentCDPError';
  }
}

/**
 * Persistent CDP Controller - handles all CDP operations
 * Runs in a dedicated control tab to maintain persistent CDP connection
 * Communicates with target tab via cross-tab messaging
 */
(function () {
  const config = {
    debug: true,
  };

  let cdpClient: CDP | null = null;
  let sessionId: string | undefined = undefined;
  let isMouseDown = false;
  let communicator: CrossTabCommunicator | null = null;

  function log(...args: any[]) {
    if (config.debug) {
      console.log('[kazeel][persistent-cdp-controller]', ...args);
    }
  }

  function error(...args: any[]) {
    console.error('[kazeel][persistent-cdp-controller]', ...args);
  }

  /**
   * Initialize the persistent CDP controller
   * @param browserFullWsEndpoint - WebSocket endpoint for browser connection
   * @param targetId - Target ID to attach to (passed from connections-workflow)
   */
  async function init(browserFullWsEndpoint: string, targetId: string): Promise<void> {
    log('Initializing persistent CDP controller');
    log('Connecting to CDP and attaching to target:', targetId);

    await connectToCDPAndAttachToTarget(browserFullWsEndpoint, targetId);
    log('CDP connection established with sessionId:', sessionId);

    // Initialize cross-tab communication
    communicator = new CrossTabCommunicator({
      channelName: 'browser-controller',
      debug: config.debug,
      timeout: 15000,
    });

    // Set up message handlers for CDP operations
    setupMessageHandlers();

    log('Persistent CDP controller initialized successfully');
  }

  /**
   * Establishes connection to CDP and attaches to existing target
   */
  async function connectToCDPAndAttachToTarget(
    wsEndpoint: string,
    targetId: string,
  ): Promise<void> {
    try {
      // Create CDP connection
      cdpClient = new CDP({ webSocketDebuggerUrl: wsEndpoint });

      log('Attaching to target:', targetId);
      const { sessionId: attachedSessionId } = await cdpClient.Target.attachToTarget({
        targetId,
        flatten: true,
      });

      sessionId = attachedSessionId;
      log('Successfully attached to target with sessionId:', sessionId);

      // Enable necessary domains
      await Promise.all([
        cdpClient.Runtime.enable(undefined, sessionId),
        cdpClient.Page.enable(undefined, sessionId),
      ]);
      await cdpClient.Page.setBypassCSP({ enabled: true }, sessionId);
      await cdpClient.Emulation.setDeviceMetricsOverride(
        {
          width: K_CUSTOM_VIEWPORT.width,
          height: K_CUSTOM_VIEWPORT.height,
          deviceScaleFactor: 1,
          mobile: false,
        },
        sessionId,
      );
      log('CDP domains enabled successfully');
    } catch (err) {
      const cdpError = new PersistentCDPError(
        `Failed to connect to CDP or attach to target: ${err}`,
        'CDP_CONNECTION_FAILED',
        'connectToCDPAndAttachToTarget',
      );
      error(cdpError.message, err);
      throw cdpError;
    }
  }

  /**
   * Set up message handlers for cross-tab communication
   */
  function setupMessageHandlers(): void {
    if (!communicator) {
      throw new PersistentCDPError(
        'Communicator not initialized',
        'COMMUNICATOR_NOT_INITIALIZED',
        'setupMessageHandlers',
      );
    }

    communicator.onMessage(async (message) => {
      log('Received message from target tab:', message.type, message.data);

      try {
        switch (message.type) {
          case 'ping':
            return { success: true, message: 'pong from persistent CDP controller' };

          case 'takeScreenshot':
            return await takeScreenshot(message.data?.enableGrayscale ?? true);

          case 'dispatchMouseMove':
            return await dispatchMouseMove(message.data.x, message.data.y);

          case 'dispatchMouseDown':
            return await dispatchMouseDown(message.data.x, message.data.y, message.data.button);

          case 'dispatchMouseUp':
            return await dispatchMouseUp(message.data.x, message.data.y, message.data.button);

          case 'dispatchMouseClick':
            return await dispatchMouseClick(message.data.x, message.data.y, message.data.button);

          case 'dispatchKeyEvent':
            return await dispatchKeyEvent(
              message.data.type,
              message.data.key,
              message.data.code,
              message.data.virtualKeyCode,
            );

          case 'insertText':
            return await insertText(message.data.text);

          case 'setupBrowserMetrics':
            return await setupBrowserMetrics(message.data.viewport);

          case 'requestNewFrame':
            return await requestNewFrame();

          case 'triggerMouseMovement':
            return await triggerMouseMovement();

          case 'getPageInfo':
            return await getPageInfo();

          case 'handleInputEvent':
            return await handleInputEvent(message.data.event);

          default:
            throw new PersistentCDPError(
              `Unknown message type: ${message.type}`,
              'UNKNOWN_MESSAGE_TYPE',
              'messageHandler',
            );
        }
      } catch (err) {
        error('Message handler error:', err);
        throw err; // Re-throw to be handled by cross-tab communicator
      }
    });

    log('Message handlers set up successfully');
  }

  /**
   * Take a screenshot using CDP
   */
  async function takeScreenshot(enableGrayscale: boolean = true): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new PersistentCDPError(
        'CDP client not initialized',
        'CDP_NOT_INITIALIZED',
        'takeScreenshot',
      );
    }

    try {
      log('Taking screenshot via CDP');
      const result = await cdpClient.Page.captureScreenshot(
        {
          format: 'webp',
          quality: 30,
          captureBeyondViewport: false,
        },
        sessionId,
      );

      log('Screenshot captured successfully');

      let finalData = result.data;
      if (enableGrayscale) {
        try {
          const startTime = performance.now();
          finalData = await convertToGrayscale(result.data);
          const processingTime = performance.now() - startTime;
          log(`Grayscale conversion applied (${processingTime.toFixed(2)}ms)`);
        } catch (error) {
          log('Grayscale conversion failed, using original screenshot:', error);
        }
      }

      return {
        success: true,
        data: finalData,
        timestamp: Date.now(),
      };
    } catch (err) {
      const screenshotError = new PersistentCDPError(
        `Failed to take screenshot: ${err}`,
        'SCREENSHOT_FAILED',
        'takeScreenshot',
      );
      error(screenshotError.message, err);
      throw screenshotError;
    }
  }

  /**
   * Dispatch mouse move event
   */
  async function dispatchMouseMove(x: number, y: number): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new PersistentCDPError(
        'CDP client not initialized',
        'CDP_NOT_INITIALIZED',
        'dispatchMouseMove',
      );
    }

    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mouseMoved',
          x: x,
          y: y,
        },
        sessionId,
      );

      log(`Mouse moved to (${x}, ${y})`);
      return { success: true };
    } catch (err) {
      const mouseError = new PersistentCDPError(
        `Failed to dispatch mouse move: ${err} (x: ${x}, y: ${y})`,
        'MOUSE_MOVE_FAILED',
        'dispatchMouseMove',
      );
      error(mouseError.message, err);
      throw mouseError;
    }
  }

  /**
   * Dispatch mouse down event
   */
  async function dispatchMouseDown(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new PersistentCDPError(
        'CDP client not initialized',
        'CDP_NOT_INITIALIZED',
        'dispatchMouseDown',
      );
    }

    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mousePressed',
          x: x,
          y: y,
          button: button,
          clickCount: 1,
          buttons: button === 'left' ? 1 : 2,
        },
        sessionId,
      );

      isMouseDown = true;
      log(`Mouse down at (${x}, ${y})`);
      return { success: true };
    } catch (err) {
      const mouseError = new PersistentCDPError(
        `Failed to dispatch mouse down: ${err} (x: ${x}, y: ${y}, button: ${button})`,
        'MOUSE_DOWN_FAILED',
        'dispatchMouseDown',
      );
      error(mouseError.message, err);
      throw mouseError;
    }
  }

  /**
   * Dispatch mouse up event
   */
  async function dispatchMouseUp(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new PersistentCDPError(
        'CDP client not initialized',
        'CDP_NOT_INITIALIZED',
        'dispatchMouseUp',
      );
    }

    try {
      await cdpClient.Input.dispatchMouseEvent(
        {
          type: 'mouseReleased',
          x: x,
          y: y,
          button: button,
          clickCount: 1,
          buttons: 0,
        },
        sessionId,
      );

      isMouseDown = false;
      log(`Mouse up at (${x}, ${y})`);
      return { success: true };
    } catch (err) {
      const mouseError = new PersistentCDPError(
        `Failed to dispatch mouse up: ${err} (x: ${x}, y: ${y}, button: ${button})`,
        'MOUSE_UP_FAILED',
        'dispatchMouseUp',
      );
      error(mouseError.message, err);
      throw mouseError;
    }
  }

  /**
   * Dispatch mouse click event
   */
  async function dispatchMouseClick(
    x: number,
    y: number,
    button: Protocol.Input.MouseButton = 'left',
  ): Promise<any> {
    try {
      await dispatchMouseDown(x, y, button);
      await new Promise((resolve) => setTimeout(resolve, 50));
      await dispatchMouseUp(x, y, button);

      log(`Mouse clicked at (${x}, ${y})`);
      return { success: true };
    } catch (err) {
      const clickError = new PersistentCDPError(
        `Failed to dispatch mouse click: ${err} (x: ${x}, y: ${y}, button: ${button})`,
        'MOUSE_CLICK_FAILED',
        'dispatchMouseClick',
      );
      error(clickError.message, err);
      throw clickError;
    }
  }

  /**
   * Dispatch key event
   */
  async function dispatchKeyEvent(
    type: 'keyDown' | 'keyUp' | 'char',
    key: string,
    code?: string,
    virtualKeyCode: number | undefined = undefined,
  ): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new PersistentCDPError(
        'CDP client not initialized',
        'CDP_NOT_INITIALIZED',
        'dispatchKeyEvent',
      );
    }

    try {
      await cdpClient.Input.dispatchKeyEvent(
        {
          type: type,
          key: key,
          code: code,
          windowsVirtualKeyCode: virtualKeyCode,
          nativeVirtualKeyCode: virtualKeyCode,
        },
        sessionId,
      );

      log(`Key event dispatched: ${type} ${key}`);
      return { success: true };
    } catch (err) {
      const keyError = new PersistentCDPError(
        `Failed to dispatch key event: ${err} (type: ${type}, key: ${key}, code: ${code})`,
        'KEY_EVENT_FAILED',
        'dispatchKeyEvent',
      );
      error(keyError.message, err);
      throw keyError;
    }
  }

  /**
   * Insert text
   */
  async function insertText(text: string): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new PersistentCDPError(
        'CDP client not initialized',
        'CDP_NOT_INITIALIZED',
        'insertText',
      );
    }

    try {
      await cdpClient.Input.insertText({ text }, sessionId);
      log(`Text inserted: ${text}`);
      return { success: true };
    } catch (err) {
      const textError = new PersistentCDPError(
        `Failed to insert text: ${err} (text: ${text})`,
        'TEXT_INSERT_FAILED',
        'insertText',
      );
      error(textError.message, err);
      throw textError;
    }
  }

  /**
   * Setup browser metrics
   */
  async function setupBrowserMetrics(viewport: { width: number; height: number }): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new PersistentCDPError(
        'CDP client not initialized',
        'CDP_NOT_INITIALIZED',
        'setupBrowserMetrics',
      );
    }

    try {
      const { windowId } = await cdpClient.Browser.getWindowForTarget({}, sessionId);
      await cdpClient.Browser.setWindowBounds(
        {
          windowId,
          bounds: {
            width: viewport.width,
            height: viewport.height + CHROME_HEADER + CHROME_SCREEN_SHARING_BANNER,
          },
        },
        sessionId,
      );

      await cdpClient.Emulation.setDeviceMetricsOverride(
        {
          width: viewport.width,
          height: viewport.height,
          deviceScaleFactor: 1,
          mobile: false,
        },
        sessionId,
      );

      log('Browser metrics set up successfully');
      return { success: true };
    } catch (err) {
      const metricsError = new PersistentCDPError(
        `Failed to setup browser metrics: ${err} (viewport: ${viewport.width}x${viewport.height})`,
        'SETUP_BROWSER_METRICS_FAILED',
        'setupBrowserMetrics',
      );
      error(metricsError.message, err);
      throw metricsError;
    }
  }

  /**
   * Request new frame using opacity technique
   */
  async function requestNewFrame(): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new PersistentCDPError(
        'CDP client not initialized',
        'CDP_NOT_INITIALIZED',
        'requestNewFrame',
      );
    }

    try {
      await cdpClient.Runtime.evaluate(
        {
          expression: `
            (async () => {
              try {
                const body = document.body || document.documentElement;
                const originalOpacity = body.style.opacity;

                body.style.opacity = '0.9999';
                await new Promise(resolve => setTimeout(resolve, 16));
                body.style.opacity = '0.99999';
                await new Promise(resolve => setTimeout(resolve, 16));
                body.style.opacity = originalOpacity || '';

                return { success: true, technique: 'opacity-change', timestamp: Date.now() };
              } catch (e) {
                return { success: false, error: e.message, timestamp: Date.now() };
              }
            })()
          `,
          awaitPromise: true,
          returnByValue: true,
        },
        sessionId,
      );

      log('New frame requested successfully');
      return { success: true };
    } catch (err) {
      const frameError = new PersistentCDPError(
        `Failed to request new frame: ${err}`,
        'REQUEST_NEW_FRAME_FAILED',
        'requestNewFrame',
      );
      error(frameError.message, err);
      throw frameError;
    }
  }

  /**
   * Trigger mouse movement for frame generation
   */
  async function triggerMouseMovement(): Promise<any> {
    try {
      await dispatchMouseMove(100, 100);
      await new Promise((resolve) => setTimeout(resolve, 16));
      await dispatchMouseMove(101, 101);
      await new Promise((resolve) => setTimeout(resolve, 16));
      await dispatchMouseMove(100, 100);

      log('Mouse movement triggered for frame generation');
      return { success: true };
    } catch (err) {
      const movementError = new PersistentCDPError(
        `Failed to trigger mouse movement: ${err}`,
        'TRIGGER_MOUSE_MOVEMENT_FAILED',
        'triggerMouseMovement',
      );
      error(movementError.message, err);
      throw movementError;
    }
  }

  /**
   * Get page information
   */
  async function getPageInfo(): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new PersistentCDPError(
        'CDP client not initialized',
        'CDP_NOT_INITIALIZED',
        'getPageInfo',
      );
    }

    try {
      const result = await cdpClient.Runtime.evaluate(
        {
          expression: `
            ({
              url: window.location.href,
              title: document.title,
              readyState: document.readyState,
              timestamp: Date.now()
            })
          `,
          returnByValue: true,
        },
        sessionId,
      );

      log('Page info retrieved successfully');
      return {
        success: true,
        data: result.result.value,
      };
    } catch (err) {
      const pageInfoError = new PersistentCDPError(
        `Failed to get page info: ${err}`,
        'GET_PAGE_INFO_FAILED',
        'getPageInfo',
      );
      error(pageInfoError.message, err);
      throw pageInfoError;
    }
  }

  /**
   * Handle input event (comprehensive event handling)
   */
  async function handleInputEvent(event: any): Promise<any> {
    if (!cdpClient || !sessionId) {
      throw new PersistentCDPError(
        'CDP client not initialized',
        'CDP_NOT_INITIALIZED',
        'handleInputEvent',
      );
    }

    try {
      log('Handling input event:', event.type, event);

      switch (event.type) {
        case 'mousedown':
          await dispatchMouseDown(event.x, event.y, event.button === 0 ? 'left' : 'right');
          break;
        case 'mouseup':
          await dispatchMouseUp(event.x, event.y, event.button === 0 ? 'left' : 'right');
          break;
        case 'mousemove':
          await dispatchMouseMove(event.x, event.y);
          break;
        case 'click':
          await dispatchMouseClick(event.x, event.y, event.button === 0 ? 'left' : 'right');
          break;

        // Character-based input handling
        case 'char-input':
          log(
            '✅ [CHAR-INPUT] Processing character:',
            event.text,
            'from:',
            event.source || 'unknown',
          );
          if (event.text !== undefined) {
            // Handle special characters
            if (event.text === '\b' || event.inputType === 'deleteContentBackward') {
              // Backspace
              await dispatchKeyEvent('keyDown', 'Backspace');
              await dispatchKeyEvent('keyUp', 'Backspace');
            } else if (event.text === '\x7F' || event.inputType === 'deleteContentForward') {
              // Delete
              await dispatchKeyEvent('keyDown', 'Delete');
              await dispatchKeyEvent('keyUp', 'Delete');
            } else if (
              event.text === '\n' ||
              event.inputType === 'insertLineBreak' ||
              event.inputType === 'insertParagraph'
            ) {
              // Enter/newline
              await dispatchKeyEvent('keyDown', 'Enter');
              await dispatchKeyEvent('keyUp', 'Enter');
            } else if (event.text && event.text.length > 0) {
              // Regular character input
              await insertText(event.text);
              log('✅ [CHAR-INPUT] Successfully inserted text:', event.text);
            } else {
              log('⚠️ [CHAR-INPUT] Empty character input, likely handled as special key');
            }
          } else {
            log('⚠️ [CHAR-INPUT] Warning: Undefined character input received');
          }
          break;

        case 'text-insert':
          log(
            '✅ [TEXT-INSERT] Processing text insertion:',
            event.text,
            'from:',
            event.source || 'unknown',
          );
          if (event.text && event.text.length > 0) {
            // Sanitize text to remove control characters
            const sanitizedText = event.text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
            if (sanitizedText.length > 0) {
              await insertText(sanitizedText);
              log('✅ [TEXT-INSERT] Successfully inserted sanitized text:', sanitizedText);
            } else {
              log('⚠️ [TEXT-INSERT] Warning: Text became empty after sanitization');
            }
          } else {
            log('⚠️ [TEXT-INSERT] Warning: Empty text insertion received');
          }
          break;

        case 'navigation-key':
          log('✅ [NAVIGATION-KEY] Processing navigation key:', event.key);
          // Handle pure navigation keys (arrows, function keys, etc.)
          await dispatchKeyEvent('keyDown', event.key, event.code, event.virtualKeyCode);
          await dispatchKeyEvent('keyUp', event.key, event.code, event.virtualKeyCode);
          break;

        // Legacy keyboard event handling (kept for compatibility)
        case 'keydown':
          log(
            '🔄 [LEGACY-KEYDOWN] Processing keydown:',
            event.key,
            'from:',
            event.source || 'unknown',
          );
          await dispatchKeyEvent('keyDown', event.key, event.code);
          break;
        case 'keyup':
          log('🔄 [LEGACY-KEYUP] Processing keyup:', event.key, 'from:', event.source || 'unknown');
          await dispatchKeyEvent('keyUp', event.key, event.code);
          break;
        case 'keypress':
          log(
            '🔄 [LEGACY-KEYPRESS] Processing keypress:',
            event.key,
            'from:',
            event.source || 'unknown',
          );
          await dispatchKeyEvent('char', event.key, event.code);
          break;
        default:
          log('Unhandled input event type:', event.type);
          const unknownEventError = new PersistentCDPError(
            `Unhandled input event type: ${event.type}`,
            'UNKNOWN_INPUT_EVENT_TYPE',
            'handleInputEvent',
          );
          return { success: false, error: unknownEventError.message };
      }

      return { success: true };
    } catch (err) {
      const inputEventError = new PersistentCDPError(
        `Failed to handle input event: ${err} (event type: ${event.type})`,
        'HANDLE_INPUT_EVENT_FAILED',
        'handleInputEvent',
      );
      error(inputEventError.message, err);
      throw inputEventError;
    }
  }

  // Expose public API
  (globalThis as any).persistentCDPController = {
    init,
  };

  log('Persistent CDP controller script loaded');
})();
