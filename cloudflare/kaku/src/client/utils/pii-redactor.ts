/**
 * PII Redaction System for Browser Automation
 *
 * This script provides comprehensive PII (Personally Identifiable Information) redaction
 * capabilities for DOM content before screenshot capture. It temporarily redacts emails
 * and phone numbers from visible text content and input fields, stores XPath mappings
 * for restoration, and provides methods to restore original content.
 *
 * Features:
 * - Email and phone number detection using regex patterns
 * - DOM text traversal excluding script/style/noscript tags
 * - Input field value redaction for text/email/tel/textarea elements
 * - XPath-based mapping for accurate content restoration
 * - Same-origin iframe content traversal (skips cross-origin for security)
 * - Error handling with graceful degradation
 * - Performance optimization with <500ms target
 */

// Ensure DOM types are available
declare global {
  interface Window {
    piiRedactor?: any;
  }
}

export interface PIIRedactionMapping {
  xpath: string;
  originalText: string;
  redactedText: string;
  elementType: 'text' | 'input' | 'textarea';
}

export interface PIIRedactionResult {
  success: boolean;
  mappings: PIIRedactionMapping[];
  redactedCount: number;
  processingTime: number;
  error?: string;
}

export interface PIIRestorationResult {
  success: boolean;
  restoredCount: number;
  failedCount: number;
  processingTime: number;
  errors: string[];
}

export class PIIRedactionError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly source: 'detection' | 'redaction' | 'restoration' = 'redaction',
  ) {
    super(`[kazeel][pii-redactor] ${message}`);
    this.name = 'PIIRedactionError';
  }
}

/**
 * PII Redactor class for handling DOM content redaction and restoration
 */
export class PIIRedactor {
  private static instance: PIIRedactor | null = null;
  private mappings: PIIRedactionMapping[] = [];
  private config = {
    debug: false,
    maxProcessingTime: 500, // 500ms performance target
    redactionPlaceholder: '***REDACTED***',
  };

  // PII detection patterns
  private readonly patterns = {
    email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    phone: /\b(\+\d{1,3}\s?)?(\(?\d{3}\)?[\s.-]?)?\d{3}[\s.-]?\d{4}\b/g,
    username: /\b[a-zA-Z0-9_.-]{4,24}\b/g,
  };

  // Elements to exclude from text traversal
  private readonly excludedTags = new Set(['SCRIPT', 'STYLE', 'NOSCRIPT']);

  /**
   * Get singleton instance
   */
  static getInstance(): PIIRedactor {
    if (!PIIRedactor.instance) {
      PIIRedactor.instance = new PIIRedactor();
    }
    return PIIRedactor.instance;
  }

  private log(...args: any[]): void {
    if (this.config.debug) {
      console.log('[kazeel][pii-redactor]', ...args);
    }
  }

  private error(...args: any[]): void {
    console.error('[kazeel][pii-redactor]', ...args);
  }

  /**
   * Generate XPath for a given DOM element
   */
  private getXPath(element: Node): string {
    if (element.nodeType === Node.DOCUMENT_NODE) {
      return '/';
    }

    if (element.nodeType === Node.TEXT_NODE) {
      const parent = element.parentNode;
      if (!parent) return '';

      const parentXPath = this.getXPath(parent);
      const textNodes = Array.from(parent.childNodes).filter(
        (node) => node.nodeType === Node.TEXT_NODE,
      ) as Text[];
      const index = textNodes.indexOf(element as Text) + 1;
      return `${parentXPath}/text()[${index}]`;
    }

    if (element.nodeType === Node.ELEMENT_NODE) {
      const elem = element as Element;
      const parent = elem.parentNode;

      if (!parent || parent.nodeType === Node.DOCUMENT_NODE) {
        return `/${elem.tagName.toLowerCase()}`;
      }

      const parentXPath = this.getXPath(parent);
      const siblings = Array.from(parent.children).filter(
        (sibling) => sibling.tagName === elem.tagName,
      );

      if (siblings.length === 1) {
        return `${parentXPath}/${elem.tagName.toLowerCase()}`;
      } else {
        const index = siblings.indexOf(elem) + 1;
        return `${parentXPath}/${elem.tagName.toLowerCase()}[${index}]`;
      }
    }

    return '';
  }

  /**
   * Find element by XPath
   */
  private getElementByXPath(xpath: string, contextDocument: Document = document): Node | null {
    try {
      const result = contextDocument.evaluate(
        xpath,
        contextDocument,
        null,
        XPathResult.FIRST_ORDERED_NODE_TYPE,
        null,
      );
      return result.singleNodeValue;
    } catch (error) {
      this.error('XPath evaluation failed:', xpath, error);
      return null;
    }
  }

  /**
   * Detect PII in text content
   */
  private detectPII(
    text: string,
    options: { isInputField: boolean } = { isInputField: false },
  ): { hasEmail: boolean; hasPhone: boolean; hasUsername: boolean; redactedText: string } {
    let redactedText = text;
    let hasEmail = false;
    let hasPhone = false;
    let hasUsername = false;

    // Check for emails
    if (this.patterns.email.test(text)) {
      hasEmail = true;
      redactedText = redactedText.replace(this.patterns.email, this.config.redactionPlaceholder);
    }

    // Check for phone numbers
    if (this.patterns.phone.test(text)) {
      hasPhone = true;
      redactedText = redactedText.replace(this.patterns.phone, this.config.redactionPlaceholder);
    }

    // Check for usernames (if input field)
    if (options.isInputField && this.patterns.username.test(redactedText)) {
      hasUsername = true;
      redactedText = redactedText.replace(this.patterns.username, this.config.redactionPlaceholder);
    }

    return { hasEmail, hasPhone, hasUsername, redactedText };
  }

  /**
   * Traverse DOM and redact PII from text nodes
   */
  private redactTextNodes(
    rootElement: Element | Document,
    contextDocument: Document = document,
  ): PIIRedactionMapping[] {
    const mappings: PIIRedactionMapping[] = [];
    const walker = contextDocument.createTreeWalker(rootElement, NodeFilter.SHOW_TEXT, {
      acceptNode: (node: Node) => {
        const parent = node.parentElement;
        if (!parent || this.excludedTags.has(parent.tagName)) {
          return NodeFilter.FILTER_REJECT;
        }

        // Skip if parent is hidden
        const style = window.getComputedStyle(parent);
        if (style.display === 'none' || style.visibility === 'hidden') {
          return NodeFilter.FILTER_REJECT;
        }

        return NodeFilter.FILTER_ACCEPT;
      },
    });

    const textNodes: Text[] = [];
    let node: Node | null;
    while ((node = walker.nextNode())) {
      textNodes.push(node as Text);
    }

    for (const textNode of textNodes) {
      const originalText = textNode.textContent || '';
      if (originalText.trim().length === 0) continue;

      const detection = this.detectPII(originalText);
      if (detection.hasEmail || detection.hasPhone) {
        const xpath = this.getXPath(textNode);
        if (xpath) {
          mappings.push({
            xpath,
            originalText,
            redactedText: detection.redactedText,
            elementType: 'text',
          });

          textNode.textContent = detection.redactedText;
        }
      }
    }

    return mappings;
  }

  /**
   * Redact PII from input field values
   */
  private redactInputFields(
    rootElement: Element | Document,
    contextDocument: Document = document,
  ): PIIRedactionMapping[] {
    const mappings: PIIRedactionMapping[] = [];
    const inputSelector = 'input[type="text"], input[type="email"], input[type="tel"], textarea';

    let inputElements: NodeListOf<HTMLInputElement | HTMLTextAreaElement>;
    if (rootElement === contextDocument) {
      inputElements = contextDocument.querySelectorAll(inputSelector);
    } else {
      inputElements = (rootElement as Element).querySelectorAll(inputSelector);
    }

    for (const element of Array.from(inputElements) as HTMLInputElement[]) {
      // Skip hidden or disabled elements
      if (element.offsetParent === null || element.disabled) continue;

      const style = window.getComputedStyle(element);
      if (style.display === 'none' || style.visibility === 'hidden') continue;

      const originalValue = element.value;
      if (!originalValue || originalValue.trim().length === 0) continue;


      const detection = this.detectPII(originalValue, { isInputField: true });

      if (detection.hasEmail || detection.hasPhone || detection.hasUsername) {
        const xpath = this.getXPath(element);
        if (xpath) {
          mappings.push({
            xpath,
            originalText: originalValue,
            redactedText: detection.redactedText,
            elementType: element.tagName.toLowerCase() === 'textarea' ? 'textarea' : 'input',
          });

          element.value = detection.redactedText;
        }
      }
    }

    return mappings;
  }

  /**
   * Check if iframe is accessible (same-origin)
   */
  private isIframeAccessible(iframe: HTMLIFrameElement): boolean {
    try {
      const doc = iframe.contentDocument;
      if (!doc) return false;
      doc.documentElement;
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Traverse and redact PII from iframes (same-origin only)
   */
  private redactIframes(rootElement: Element | Document): PIIRedactionMapping[] {
    const mappings: PIIRedactionMapping[] = [];

    let iframes: NodeListOf<HTMLIFrameElement>;
    if (rootElement === document) {
      iframes = document.querySelectorAll('iframe');
    } else {
      iframes = (rootElement as Element).querySelectorAll('iframe');
    }

    for (const iframe of Array.from(iframes) as HTMLIFrameElement[]) {
      try {
        // Skip hidden iframes
        if (iframe.offsetParent === null) continue;

        const style = window.getComputedStyle(iframe);
        if (style.display === 'none' || style.visibility === 'hidden') continue;

        if (!this.isIframeAccessible(iframe)) {
          if (this.config.debug) {
            this.log(
              'Skipping cross-origin iframe:',
              iframe.src || iframe.getAttribute('src') || 'unknown',
            );
          }
          continue;
        }

        const iframeDoc = iframe.contentDocument!;

        // Recursively redact iframe content
        const textMappings = this.redactTextNodes(iframeDoc, iframeDoc);
        const inputMappings = this.redactInputFields(iframeDoc, iframeDoc);
        const nestedIframeMappings = this.redactIframes(iframeDoc);

        mappings.push(...textMappings, ...inputMappings, ...nestedIframeMappings);
      } catch (error) {
        // Log error but continue with other iframes - only in debug mode
        if (this.config.debug) {
          this.log(
            'Error processing iframe (skipping):',
            iframe.src || 'unknown',
            error instanceof Error ? error.message : error,
          );
        }
      }
    }

    return mappings;
  }

  /**
   * Main redaction method - redacts all PII from the page
   */
  async redactPII(): Promise<PIIRedactionResult> {
    const startTime = performance.now();

    try {
      // Clear previous mappings
      this.mappings = [];

      // Redact main document
      const textMappings = this.redactTextNodes(document);
      const inputMappings = this.redactInputFields(document);
      const iframeMappings = this.redactIframes(document);

      // Combine all mappings
      this.mappings = [...textMappings, ...inputMappings, ...iframeMappings];

      const processingTime = performance.now() - startTime;

      // Check performance target
      if (processingTime > this.config.maxProcessingTime) {
        this.error(
          `Performance target exceeded: ${processingTime.toFixed(2)}ms > ${this.config.maxProcessingTime}ms`,
        );
      }

      return {
        success: true,
        mappings: [...this.mappings], // Return copy
        redactedCount: this.mappings.length,
        processingTime,
      };
    } catch (error) {
      const processingTime = performance.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.error('PII redaction failed:', errorMessage);

      return {
        success: false,
        mappings: [],
        redactedCount: 0,
        processingTime,
        error: errorMessage,
      };
    }
  }

  /**
   * Restore original content using stored mappings
   */
  async restorePII(): Promise<PIIRestorationResult> {
    const startTime = performance.now();
    const errors: string[] = [];
    let restoredCount = 0;
    let failedCount = 0;

    try {
      for (const mapping of this.mappings) {
        try {
          const element = this.getElementByXPath(mapping.xpath);

          if (!element) {
            const error = `Element not found for XPath: ${mapping.xpath}`;
            errors.push(error);
            failedCount++;
            continue;
          }

          if (mapping.elementType === 'text') {
            if (element.nodeType === Node.TEXT_NODE) {
              element.textContent = mapping.originalText;
              restoredCount++;
            } else {
              const error = `XPath points to non-text node: ${mapping.xpath}`;
              errors.push(error);
              failedCount++;
            }
          } else if (mapping.elementType === 'input' || mapping.elementType === 'textarea') {
            if (element instanceof HTMLInputElement || element instanceof HTMLTextAreaElement) {
              element.value = mapping.originalText;
              restoredCount++;
            } else {
              const error = `XPath points to non-input element: ${mapping.xpath}`;
              errors.push(error);
              failedCount++;
            }
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          errors.push(`Failed to restore ${mapping.xpath}: ${errorMessage}`);
          failedCount++;
        }
      }

      // Clear mappings after restoration
      this.mappings = [];

      const processingTime = performance.now() - startTime;

      return {
        success: failedCount === 0,
        restoredCount,
        failedCount,
        processingTime,
        errors,
      };
    } catch (error) {
      const processingTime = performance.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      this.error('PII restoration failed:', errorMessage);

      return {
        success: false,
        restoredCount,
        failedCount: this.mappings.length - restoredCount,
        processingTime,
        errors: [...errors, errorMessage],
      };
    }
  }

  /**
   * Get current redaction status
   */
  getRedactionStatus(): { isRedacted: boolean; mappingCount: number } {
    return {
      isRedacted: this.mappings.length > 0,
      mappingCount: this.mappings.length,
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<typeof this.config>): void {
    Object.assign(this.config, newConfig);
    this.log('Configuration updated:', newConfig);
  }
}

/**
 * Global PII Redactor instance and API
 * Auto-initializes when script loads (Phase 1 essential script)
 */
(function initializePIIRedactor() {
  const config = {
    debug: false,
  };

  function log(...args: any[]): void {
    if (config.debug) {
      console.log('[kazeel][pii-redactor-global]', ...args);
    }
  }

  function error(...args: any[]): void {
    console.error('[kazeel][pii-redactor-global]', ...args);
  }

  try {
    // Get singleton instance
    const piiRedactor = PIIRedactor.getInstance();

    // Expose global API for cross-tab communication
    (globalThis as any).piiRedactor = {
      /**
       * Redact all PII from the current page
       */
      async redactPII(): Promise<PIIRedactionResult> {
        return await piiRedactor.redactPII();
      },

      /**
       * Restore all previously redacted PII
       */
      async restorePII(): Promise<PIIRestorationResult> {
        return await piiRedactor.restorePII();
      },

      /**
       * Get current redaction status
       */
      getRedactionStatus(): { isRedacted: boolean; mappingCount: number } {
        return piiRedactor.getRedactionStatus();
      },

      /**
       * Update configuration
       */
      updateConfig(newConfig: any): void {
        piiRedactor.updateConfig(newConfig);
      },

      /**
       * Test PII detection (for debugging)
       */
      testPIIDetection(text: string): {
        hasEmail: boolean;
        hasPhone: boolean;
        redactedText: string;
      } {
        const patterns = {
          email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
          phone: /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g,
        };

        let redactedText = text;
        let hasEmail = false;
        let hasPhone = false;

        if (patterns.email.test(text)) {
          hasEmail = true;
          redactedText = redactedText.replace(patterns.email, '***REDACTED***');
        }

        if (patterns.phone.test(text)) {
          hasPhone = true;
          redactedText = redactedText.replace(patterns.phone, '***REDACTED***');
        }

        return { hasEmail, hasPhone, redactedText };
      },
    };

    log('✓ PII Redactor global API initialized');
  } catch (err) {
    error('Failed to initialize PII Redactor global API:', err);
  }
})();
