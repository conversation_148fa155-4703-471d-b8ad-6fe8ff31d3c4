# Cross-Tab Communicator Architecture

## Overview

The Cross-Tab Communicator is a critical component of KAKU's two-tab browser automation architecture. It enables seamless bidirectional communication between the **Control Tab** (which maintains persistent CDP connections) and the **Target Tab** (which handles user interactions and UI rendering).

## High-Level Architecture

```mermaid
graph TB
    subgraph "Control Tab"
        PersistentCDP[Persistent CDP Controller]
        ControlComm[CrossTabCommunicator Instance]
        CDP[CDP Session]
    end

    subgraph "Target Tab"
        ProxyController[Browser Controller Proxy]
        TargetComm[CrossTabCommunicator Instance]
        UI[User Interface & Interactions]
    end

    subgraph "Browser Infrastructure"
        BC[BroadcastChannel API]
        DOM[DOM Events Fallback]
    end

    TargetComm <-->|BroadcastChannel| ControlComm
    TargetComm -.->|Fallback| DOM
    ControlComm -.->|Fallback| DOM

    ProxyController --> TargetComm
    TargetComm --> ControlComm
    ControlComm --> PersistentCDP
    PersistentCDP --> CDP

    CDP -->|Results| PersistentCDP
    PersistentCDP --> ControlComm
    ControlComm --> TargetComm
    TargetComm --> ProxyController
    ProxyController --> UI
```

## Message Flow Architecture

### 1. Request-Response Pattern

The communicator implements a **request-response pattern** with automatic timeout handling:

```typescript
// Target Tab sends request
const result = await communicator.sendMessage('takeScreenshot', {});

// Control Tab receives and processes
communicator.onMessage(async (message) => {
  if (message.type === 'takeScreenshot') {
    const screenshot = await cdpClient.Page.captureScreenshot();
    return { success: true, data: screenshot.data };
  }
});
```

### 2. Message Structure

All messages follow a standardized structure:

```typescript
interface CrossTabMessage {
  id: string; // Unique identifier for request-response matching
  type: string; // Message type (e.g., 'takeScreenshot', 'dispatchMouseClick')
  data?: any; // Payload data
  timestamp: number; // Creation timestamp
}

interface CrossTabResponse {
  id: string; // Matches original message ID
  type: 'response'; // Always 'response' for replies
  result: any; // Response data or error
  timestamp: number; // Response timestamp
}
```

## Bidirectional Communication Flow

### Forward Flow: Target → Control

1. **User Action Trigger**: User interacts with UI in Target Tab
2. **Proxy Request**: Browser Controller Proxy calls `communicator.sendMessage()`
3. **Message Creation**: Communicator generates unique ID and wraps data
4. **Channel Broadcast**: Message sent via BroadcastChannel
5. **Control Reception**: Control Tab's communicator receives message
6. **CDP Execution**: Persistent CDP Controller executes actual browser operation
7. **Response Generation**: Result wrapped in response message
8. **Response Broadcast**: Response sent back via BroadcastChannel
9. **Promise Resolution**: Target Tab's pending promise resolves with result

### Reverse Flow: Control → Target

While less common, the architecture supports Control Tab initiating communication:

1. **CDP Event**: Control Tab detects browser event or needs to notify Target Tab
2. **Notification Send**: Control Tab sends message to Target Tab
3. **UI Update**: Target Tab updates interface or handles notification

## Technical Implementation Details

### BroadcastChannel API

Primary communication mechanism:

- **Cross-origin support**: Works across different origins
- **Real-time**: Near-instantaneous message delivery
- **Browser native**: No polling or external dependencies

### Message Routing & Timeout Handling

```typescript
async sendMessage(type: string, data: any = {}): Promise<any> {
  return new Promise((resolve, reject) => {
    const id = `msg_${++this.messageId}_${Date.now()}`;

    // Store pending message for response matching
    this.pendingMessages.set(id, { resolve, reject });

    // Set timeout to prevent hanging
    setTimeout(() => {
      if (this.pendingMessages.has(id)) {
        this.pendingMessages.delete(id);
        reject(new Error(`Timeout for message type: ${type}`));
      }
    }, this.options.timeout);

    // Broadcast message
    this.channel.postMessage({ id, type, data, timestamp: Date.now() });
  });
}
```

### Type Safety & Validation

The communicator includes runtime type checking:

```typescript
private isValidMessage(data: any): data is CrossTabMessage | CrossTabResponse {
  return (
    data &&
    typeof data === 'object' &&
    'id' in data &&
    'type' in data &&
    'timestamp' in data
  );
}
```

## Supported Message Types

### CDP Operations

- `takeScreenshot` - Capture page screenshot
- `dispatchMouseMove/Down/Up/Click` - Mouse interactions
- `dispatchKeyEvent` - Keyboard input
- `insertText` - Text insertion
- `setupBrowserMetrics` - Viewport configuration

### Page Management

- `getPageInfo` - Retrieve page metadata
- `requestNewFrame` - Force frame generation
- `triggerMouseMovement` - Generate UI updates

### Input Handling

- `handleInputEvent` - Comprehensive input processing
- Character-based input (`char-input`)
- Text insertion (`text-insert`)
- Navigation keys (`navigation-key`)

## Error Handling & Resilience

### Timeout Management

- Configurable timeout per message (default: 15 seconds)
- Automatic cleanup of pending messages
- Clear error messages for debugging

### Connection Resilience

- Graceful handling of channel closure
- Automatic rejection of pending messages on disconnect
- Type validation prevents malformed message processing

### Debug Support

- Comprehensive logging with configurable debug levels
- Message tracing with timestamps
- Error context preservation

## Configuration Options

```typescript
interface CrossTabCommunicationOptions {
  channelName: string; // BroadcastChannel name
  timeout?: number; // Message timeout (ms)
  debug?: boolean; // Enable debug logging
  fallbackToDOMEvents?: boolean; // Enable DOM events fallback
}
```

## Performance Characteristics

- **Latency**: ~1-5ms for local tab communication
- **Throughput**: Handles hundreds of messages per second
- **Memory**: Minimal overhead, automatic cleanup
- **CPU**: Negligible impact on browser performance

## Security Considerations

- **Same-origin policy**: BroadcastChannel respects browser security
- **Message validation**: Runtime type checking prevents injection
- **Timeout protection**: Prevents resource exhaustion
- **Isolated execution**: Each tab maintains separate execution context

This architecture enables KAKU to maintain persistent CDP connections while providing responsive user interactions, solving the fundamental challenge of browser automation in dynamic web environments.

## Sequence Diagrams

### Typical User Interaction Flow

```mermaid
sequenceDiagram
    participant User
    participant TargetTab as Target Tab (UI)
    participant TargetComm as Target Communicator
    participant ControlComm as Control Communicator
    participant ControlTab as Control Tab (CDP)
    participant Browser

    User->>TargetTab: Click button
    TargetTab->>TargetComm: sendMessage('dispatchMouseClick', {x, y})
    TargetComm->>ControlComm: BroadcastChannel message
    ControlComm->>ControlTab: onMessage handler
    ControlTab->>Browser: CDP Input.dispatchMouseEvent
    Browser-->>ControlTab: Success response
    ControlTab-->>ControlComm: Return success
    ControlComm-->>TargetComm: BroadcastChannel response
    TargetComm-->>TargetTab: Promise resolves
    TargetTab-->>User: UI feedback
```

### Screenshot Capture Flow

```mermaid
sequenceDiagram
    participant UI as Target Tab UI
    participant TC as Target Communicator
    participant CC as Control Communicator
    participant CDP as Control CDP

    UI->>TC: takeScreenshot()
    TC->>CC: {type: 'takeScreenshot', id: 'msg_123'}
    CC->>CDP: Page.captureScreenshot()
    CDP-->>CC: {data: base64Image}
    CC-->>TC: {type: 'response', id: 'msg_123', result: {success: true, data}}
    TC-->>UI: Promise resolves with image data
```

## Implementation Examples

### Setting Up Communication

```typescript
// In Control Tab (persistent-cdp-controller.ts)
const communicator = new CrossTabCommunicator({
  channelName: 'browser-controller',
  debug: true,
  timeout: 15000,
});

communicator.onMessage(async (message) => {
  switch (message.type) {
    case 'takeScreenshot':
      return await takeScreenshot();
    case 'dispatchMouseClick':
      return await dispatchMouseClick(message.data.x, message.data.y);
    // ... other handlers
  }
});
```

```typescript
// In Target Tab (browser-controller-proxy.ts)
const communicator = new CrossTabCommunicator({
  channelName: 'browser-controller',
  debug: true,
  timeout: 15000,
});

async function takeScreenshot() {
  const result = await communicator.sendMessage('takeScreenshot');
  return result;
}
```

### Error Handling Patterns

```typescript
try {
  const result = await communicator.sendMessage('complexOperation', data);
  if (result.success) {
    // Handle success
    processResult(result.data);
  } else {
    // Handle operation failure
    console.error('Operation failed:', result.error);
  }
} catch (error) {
  // Handle communication failure (timeout, channel closed, etc.)
  console.error('Communication error:', error.message);
}
```

## Troubleshooting Guide

### Common Issues

1. **Communication Timeout**

   - **Symptom**: Messages timeout after 15 seconds
   - **Causes**: Control tab not initialized, BroadcastChannel not supported
   - **Solution**: Check control tab initialization, verify browser compatibility

2. **Message Not Received**

   - **Symptom**: Messages sent but no response
   - **Causes**: Different channel names, isolated world issues
   - **Solution**: Verify channel name consistency, check isolated world setup

3. **Type Errors**
   - **Symptom**: Messages ignored or cause errors
   - **Causes**: Malformed message structure
   - **Solution**: Ensure message follows CrossTabMessage interface

### Debug Techniques

```typescript
// Enable debug logging
const communicator = new CrossTabCommunicator({
  channelName: 'browser-controller',
  debug: true, // This enables detailed logging
});

// Monitor message flow
communicator.onMessage(async (message) => {
  console.log('Received:', message.type, message.data);
  const result = await handleMessage(message);
  console.log('Responding with:', result);
  return result;
});
```

## Browser Compatibility

| Browser      | BroadcastChannel Support | Notes           |
| ------------ | ------------------------ | --------------- |
| Chrome 54+   | ✅ Full Support          | Primary target  |
| Firefox 38+  | ✅ Full Support          | Tested          |
| Safari 15.4+ | ✅ Full Support          | Limited testing |
| Edge 79+     | ✅ Full Support          | Chromium-based  |

## Performance Optimization

### Message Batching

For high-frequency operations, consider batching:

```typescript
// Instead of multiple individual calls
await communicator.sendMessage('dispatchMouseMove', { x: 100, y: 100 });
await communicator.sendMessage('dispatchMouseMove', { x: 101, y: 101 });

// Batch into single operation
await communicator.sendMessage('batchMouseMoves', [
  { x: 100, y: 100 },
  { x: 101, y: 101 },
]);
```

### Memory Management

The communicator automatically cleans up:

- Completed message promises
- Timed-out message handlers
- Closed channel resources

## Future Enhancements

- **Message Compression**: For large payloads
- **Priority Queuing**: Critical vs. non-critical messages
- **Retry Logic**: Automatic retry for failed operations
- **Performance Metrics**: Built-in latency and throughput monitoring
